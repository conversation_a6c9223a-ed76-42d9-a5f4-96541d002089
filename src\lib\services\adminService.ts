import { api } from '@/lib/utils/apiClient'
import { API_CONFIG } from '@/lib/config/api'

export interface DashboardStats {
  totalTeachers: number
  totalSchedules: number
  totalSubjects: number
  totalClasses: number
  totalRooms: number
  totalDepartments: number
  currentSemester: string
  weeklySchedules: number
}

export interface SystemInfo {
  version: string
  environment: string
  database: {
    type: string
    version: string
    size: string
  }
  server: {
    memory: string
    cpu: string
    uptime: string
  }
  dataStatistics: {
    totalUsers: number
    totalSchedules: number
    totalSubjects: number
    totalClasses: number
    totalRooms: number
    totalDepartments: number
  }
}

export interface SystemStatistics {
  userStatistics: {
    totalUsers: number
    activeUsers: number
    adminUsers: number
    teacherUsers: number
  }
  scheduleStatistics: {
    totalSchedules: number
    approvedSchedules: number
    pendingSchedules: number
    rejectedSchedules: number
  }
  dataStatistics: {
    totalSubjects: number
    totalClasses: number
    totalRooms: number
    totalDepartments: number
  }
  systemHealth: {
    status: 'healthy' | 'warning' | 'error'
    uptime: string
    memoryUsage: number
    diskUsage: number
  }
}

export interface BackupInfo {
  id: string
  fileName: string
  size: number
  createdAt: string
  type: 'manual' | 'auto'
  status: 'completed' | 'failed' | 'in_progress'
}

class AdminService {
  // Lấy dữ liệu dashboard
  async getDashboardData(): Promise<DashboardStats> {
    const response = await api.get(API_CONFIG.ENDPOINTS.ADMIN.DASHBOARD)
    return response.data
  }

  // Lấy thông tin hệ thống
  async getSystemInfo(): Promise<SystemInfo> {
    const response = await apiCall('/admin/system-info')
    return response.data
  }

  // Lấy thống kê hệ thống
  async getSystemStatistics(): Promise<SystemStatistics> {
    const response = await apiCall('/admin/statistics')
    return response.data
  }

  // Khởi tạo dữ liệu mẫu
  async initializeData(): Promise<string> {
    const response = await apiCall('/admin/initialize-data', {
      method: 'POST'
    })
    return response.message
  }

  // Tạo backup
  async createBackup(): Promise<string> {
    const response = await apiCall('/admin/backup', {
      method: 'POST'
    })
    return response.data
  }

  // Lấy danh sách backup
  async getBackupList(): Promise<BackupInfo[]> {
    const response = await apiCall('/admin/backups')
    return response.data
  }

  // Xóa backup
  async deleteBackup(backupId: string): Promise<void> {
    await apiCall(`/admin/backups/${backupId}`, {
      method: 'DELETE'
    })
  }

  // Restore từ backup
  async restoreBackup(backupId: string): Promise<string> {
    const response = await apiCall(`/admin/backups/${backupId}/restore`, {
      method: 'POST'
    })
    return response.message
  }

  // Kiểm tra tình trạng hệ thống
  async checkSystemHealth(): Promise<any> {
    const response = await apiCall('/admin/health')
    return response.data
  }

  // Dọn dẹp dữ liệu cũ
  async cleanupOldData(): Promise<string> {
    const response = await apiCall('/admin/cleanup', {
      method: 'POST'
    })
    return response.message
  }

  // Tối ưu hóa database
  async optimizeDatabase(): Promise<string> {
    const response = await apiCall('/admin/optimize', {
      method: 'POST'
    })
    return response.message
  }

  // Lấy logs hệ thống
  async getSystemLogs(level?: string, limit?: number): Promise<any[]> {
    const params = new URLSearchParams()
    if (level) params.append('level', level)
    if (limit) params.append('limit', limit.toString())

    const endpoint = `/admin/logs${params.toString() ? '?' + params.toString() : ''}`
    const response = await apiCall(endpoint)
    return response.data
  }

  // Cập nhật cấu hình hệ thống
  async updateSystemConfig(config: Record<string, any>): Promise<void> {
    await apiCall('/admin/config', {
      method: 'PUT',
      body: JSON.stringify(config)
    })
  }

  // Lấy cấu hình hệ thống
  async getSystemConfig(): Promise<Record<string, any>> {
    const response = await apiCall('/admin/config')
    return response.data
  }

  // Gửi thông báo hệ thống
  async sendSystemNotification(message: string, type: 'info' | 'warning' | 'error'): Promise<void> {
    await apiCall('/admin/notifications', {
      method: 'POST',
      body: JSON.stringify({ message, type })
    })
  }

  // Lấy thống kê sử dụng theo thời gian
  async getUsageStatistics(startDate: string, endDate: string): Promise<any> {
    const response = await apiCall(`/admin/usage-stats?start=${startDate}&end=${endDate}`)
    return response.data
  }
}

export const adminService = new AdminService()
