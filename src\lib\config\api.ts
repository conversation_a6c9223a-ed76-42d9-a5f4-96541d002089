// API Configuration
export const API_CONFIG = {
  // Base URL cho API
  BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8080',
  
  // API endpoints
  ENDPOINTS: {
    // Authentication
    AUTH: {
      LOGIN: '/api/auth/login',
      LOGOUT: '/api/auth/logout',
      REFRESH: '/api/auth/refresh',
      PROFILE: '/api/auth/profile'
    },
    
    // Admin endpoints
    ADMIN: {
      DASHBOARD: '/api/admin/dashboard',
      BACKUP: '/api/admin/backup',
      INITIALIZE: '/api/admin/initialize'
    },
    
    // Master data endpoints
    MASTER_DATA: {
      DEPARTMENTS: '/api/master-data/departments',
      SUBJECTS: '/api/master-data/subjects',
      CLASSES: '/api/master-data/classes',
      ROOMS: '/api/master-data/rooms',
      TEACHERS: '/api/master-data/teachers',
      CAMPUSES: '/api/master-data/campuses'
    },
    
    // Academic year and semester endpoints
    ACADEMIC: {
      YEARS: '/api/academic-years',
      SEMESTERS: '/api/semesters'
    },
    
    // Schedule endpoints
    SCHEDULES: {
      BASE: '/api/schedules',
      TEACHER: '/api/schedules/teacher',
      IMPORT: '/api/schedules/import',
      EXPORT: '/api/schedules/export'
    },
    
    // Teaching hours endpoints
    TEACHING_HOURS: {
      BASE: '/api/teaching-hours',
      TEACHER: '/api/teaching-hours/teacher',
      REPORT: '/api/teaching-hours/report'
    },
    
    // Import/Export endpoints
    IMPORT_EXPORT: {
      IMPORT: '/api/import',
      EXPORT: '/api/export',
      TEMPLATES: '/api/templates',
      HISTORY: '/api/import/history'
    },
    
    // Reports endpoints
    REPORTS: {
      TEACHER: '/api/reports/teacher',
      ADMIN: '/api/reports/admin',
      EXPORT: '/api/reports/export'
    }
  },
  
  // Request timeout (milliseconds)
  TIMEOUT: 30000,
  
  // Default headers
  DEFAULT_HEADERS: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
}

// Helper function để tạo full URL
export const createApiUrl = (endpoint: string): string => {
  return `${API_CONFIG.BASE_URL}${endpoint}`
}

// Helper function để lấy headers với token
export const getAuthHeaders = (): Record<string, string> => {
  const token = typeof window !== 'undefined' ? localStorage.getItem('token') : null
  
  return {
    ...API_CONFIG.DEFAULT_HEADERS,
    ...(token && { Authorization: `Bearer ${token}` })
  }
}

// Helper function để tạo request config
export const createRequestConfig = (options: {
  method?: string
  headers?: Record<string, string>
  body?: any
} = {}): RequestInit => {
  const config: RequestInit = {
    method: options.method || 'GET',
    headers: {
      ...getAuthHeaders(),
      ...options.headers
    }
  }
  
  if (options.body) {
    if (options.body instanceof FormData) {
      // Nếu là FormData, không set Content-Type (browser sẽ tự set)
      delete config.headers!['Content-Type']
      config.body = options.body
    } else {
      // Nếu là object, stringify thành JSON
      config.body = JSON.stringify(options.body)
    }
  }
  
  return config
}

// Environment-specific configurations
export const ENV_CONFIG = {
  isDevelopment: process.env.NODE_ENV === 'development',
  isProduction: process.env.NODE_ENV === 'production',
  isTest: process.env.NODE_ENV === 'test'
}

// API response types
export interface ApiResponse<T = any> {
  success: boolean
  message: string
  data: T
  metadata?: any
}

export interface ApiError {
  success: false
  message: string
  error?: string
  details?: any
}

// Pagination types
export interface PaginationParams {
  page?: number
  size?: number
  sort?: string
  direction?: 'asc' | 'desc'
}

export interface PaginatedResponse<T> {
  content: T[]
  totalElements: number
  totalPages: number
  size: number
  number: number
  first: boolean
  last: boolean
}

export default API_CONFIG
