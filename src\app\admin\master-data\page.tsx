"use client"

import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import QuickActionCard from '@/components/dashboard/QuickActionCard'
import {
  Building,
  BookOpen,
  GraduationCap,
  User,
  MapPin,
  Database,
  Upload,
  Download,
  FileText,
  Settings
} from "lucide-react"

export default function MasterDataPage() {
  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Dữ Liệu <PERSON></h1>
          <p className="text-muted-foreground">
            Quản lý các dữ liệu danh mục cơ bản của hệ thống
          </p>
        </div>
        <div className="flex space-x-2">
          <Button variant="outline" size="sm">
            <Upload className="mr-2 h-4 w-4" />
            Import
          </Button>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Export
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Khoa</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">Đang hoạt động</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Môn học</CardTitle>
            <BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">245</div>
            <p className="text-xs text-muted-foreground">Trong hệ thống</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Lớp học</CardTitle>
            <GraduationCap className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">89</div>
            <p className="text-xs text-muted-foreground">Đang học</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Giảng viên</CardTitle>
            <User className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">156</div>
            <p className="text-xs text-muted-foreground">Đang giảng dạy</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Phòng học</CardTitle>
            <Building className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">67</div>
            <p className="text-xs text-muted-foreground">Có thể sử dụng</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Cơ sở</CardTitle>
            <MapPin className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">Địa điểm</p>
          </CardContent>
        </Card>
      </div>

      {/* Management Sections */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <QuickActionCard
          title="Quản Lý Khoa"
          description="Thêm, sửa, xóa thông tin các khoa và phòng ban"
          icon={Building}
          href="/admin/master-data/departments"
          color="bg-blue-500"
        />
        
        <QuickActionCard
          title="Quản Lý Môn Học"
          description="Quản lý danh sách môn học và chương trình đào tạo"
          icon={BookOpen}
          href="/admin/master-data/subjects"
          color="bg-green-500"
        />
        
        <QuickActionCard
          title="Quản Lý Lớp Học"
          description="Thông tin lớp học, sinh viên và khóa học"
          icon={GraduationCap}
          href="/admin/master-data/classes"
          color="bg-purple-500"
        />
        
        <QuickActionCard
          title="Quản Lý Giảng Viên"
          description="Thông tin giảng viên, chuyên môn và phân công"
          icon={User}
          href="/admin/master-data/teachers"
          color="bg-red-500"
        />
        
        <QuickActionCard
          title="Quản Lý Phòng Học"
          description="Phòng học, trang thiết bị và sức chứa"
          icon={Building}
          href="/admin/master-data/rooms"
          color="bg-orange-500"
        />
        
        <QuickActionCard
          title="Quản Lý Cơ Sở"
          description="Các cơ sở và địa điểm giảng dạy"
          icon={MapPin}
          href="/admin/master-data/campuses"
          color="bg-indigo-500"
        />
      </div>

      {/* Import/Export Section */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Database className="mr-2 h-5 w-5" />
            Import/Export Dữ Liệu
          </CardTitle>
          <CardDescription>
            Quản lý import và export dữ liệu danh mục từ/ra file Excel
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <h4 className="font-medium">Import Templates</h4>
              <div className="space-y-2">
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  Template Khoa
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  Template Môn Học
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  Template Lớp Học
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Download className="mr-2 h-4 w-4" />
                  Template Giảng Viên
                </Button>
              </div>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-medium">Export Data</h4>
              <div className="space-y-2">
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Upload className="mr-2 h-4 w-4" />
                  Export Tất Cả
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <FileText className="mr-2 h-4 w-4" />
                  Báo Cáo Tổng Hợp
                </Button>
                <Button variant="outline" className="w-full justify-start" size="sm">
                  <Settings className="mr-2 h-4 w-4" />
                  Cấu Hình Import
                </Button>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
