"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import {
  Users,
  BookOpen,
  GraduationCap,
  Building,
  Calendar,
  Database,
  Download,
  Upload,
  Settings,
  BarChart3,
  RefreshCw,
  History,
  ArrowRight,
  Clock,
  FileText
} from "lucide-react"
import StatsCard from '@/components/dashboard/StatsCard'
import QuickActionCard from '@/components/dashboard/QuickActionCard'
import { adminService, DashboardStats } from "@/lib/services/adminService"

export default function AdminPage() {
  const router = useRouter()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [loading, setLoading] = useState(false)

  useEffect(() => {
    loadDashboardData()
  }, [])

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      const data = await adminService.getDashboardData()
      setStats(data)
    } catch (error) {
      console.error('Error loading dashboard data:', error)
    } finally {
      setLoading(false)
    }
  }

  const handleBackup = async () => {
    try {
      const backupPath = await adminService.createBackup()
      alert(`Sao lưu thành công: ${backupPath}`)
    } catch (error) {
      alert('Lỗi khi tạo sao lưu')
    }
  }

  const handleInitializeData = async () => {
    if (confirm('Bạn có chắc chắn muốn khởi tạo lại dữ liệu? Điều này sẽ xóa tất cả dữ liệu hiện tại.')) {
      try {
        await adminService.initializeData()
        alert('Khởi tạo dữ liệu thành công')
        loadDashboardData()
      } catch (error) {
        alert('Lỗi khi khởi tạo dữ liệu')
      }
    }
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Dashboard Admin</h1>
          <p className="text-muted-foreground">
            Tổng quan hệ thống và quản lý dữ liệu
          </p>
        </div>
        <div className="flex space-x-2">
          <Button onClick={handleBackup} variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Sao Lưu
          </Button>
          <Button onClick={handleInitializeData} variant="outline">
            <Database className="mr-2 h-4 w-4" />
            Khởi Tạo Dữ Liệu
          </Button>
        </div>
      </div>

      {/* Statistics Cards */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <StatsCard
          title="Tổng Giảng Viên"
          value={stats?.totalTeachers || 0}
          description="Đang hoạt động"
          icon={Users}
        />
        <StatsCard
          title="Tổng Lịch Giảng"
          value={stats?.totalSchedules || 0}
          description="Trong hệ thống"
          icon={Calendar}
        />
        <StatsCard
          title="Tổng Môn Học"
          value={stats?.totalSubjects || 0}
          description="Đã đăng ký"
          icon={BookOpen}
        />
        <StatsCard
          title="Tổng Lớp Học"
          value={stats?.totalClasses || 0}
          description="Đang hoạt động"
          icon={GraduationCap}
        />
      </div>

      {/* Current Semester Info */}
      {stats?.currentSemester && (
        <Card>
          <CardHeader>
            <CardTitle>Học Kỳ Hiện Tại</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              <Badge variant="default">{stats.currentSemester}</Badge>
              <span className="text-sm text-muted-foreground">
                {stats.weeklySchedules} lịch giảng trong tuần này
              </span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Management Tabs */}
      <Tabs defaultValue="overview" className="space-y-4">
        <TabsList>
          <TabsTrigger value="overview">Tổng Quan</TabsTrigger>
          <TabsTrigger value="academic">Năm Học & Học Kỳ</TabsTrigger>
          <TabsTrigger value="master-data">Dữ Liệu Danh Mục</TabsTrigger>
          <TabsTrigger value="import-export">Import/Export</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            <QuickActionCard
              title="Import & Đồng bộ"
              description="Quản lý import và đồng bộ dữ liệu từ hệ thống bên ngoài"
              icon={Database}
              href="/admin/import-management"
              color="bg-blue-500"
            />
            <QuickActionCard
              title="Năm Học & Học Kỳ"
              description="Quản lý năm học và học kỳ"
              icon={Calendar}
              href="/admin/academic-years"
              color="bg-green-500"
            />
            <QuickActionCard
              title="Quản Lý Khoa"
              description="Quản lý các khoa/phòng ban"
              icon={Building}
              href="/admin/departments"
              color="bg-purple-500"
            />
            <QuickActionCard
              title="Báo Cáo"
              description="Xem báo cáo và thống kê hệ thống"
              icon={BarChart3}
              href="/admin/reports"
              color="bg-orange-500"
            />
            <QuickActionCard
              title="Cài Đặt"
              description="Cấu hình hệ thống và tham số"
              icon={Settings}
              href="/admin/settings"
              color="bg-gray-500"
            />
            <QuickActionCard
              title="Tài Liệu"
              description="Hướng dẫn sử dụng và tài liệu"
              icon={FileText}
              href="/admin/documentation"
              color="bg-indigo-500"
            />
          </div>
        </TabsContent>

        <TabsContent value="academic" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle>Năm Học</CardTitle>
                <CardDescription>
                  Quản lý các năm học/niên khóa
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  className="w-full"
                  onClick={() => router.push('/admin/academic-years')}
                >
                  Quản Lý Năm Học
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Học Kỳ</CardTitle>
                <CardDescription>
                  Quản lý các học kỳ trong năm
                </CardDescription>
              </CardHeader>
              <CardContent>
                <Button
                  className="w-full"
                  onClick={() => router.push('/admin/semesters')}
                >
                  Quản Lý Học Kỳ
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="master-data" className="space-y-4">
          <div className="grid gap-4 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle>Khoa/Phòng Ban</CardTitle>
              </CardHeader>
              <CardContent>
                <Button
                  className="w-full"
                  onClick={() => router.push('/admin/departments')}
                >
                  Quản Lý Khoa
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Môn Học</CardTitle>
              </CardHeader>
              <CardContent>
                <Button className="w-full">Quản Lý Môn Học</Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Lớp Học</CardTitle>
              </CardHeader>
              <CardContent>
                <Button className="w-full">Quản Lý Lớp Học</Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Phòng Học</CardTitle>
              </CardHeader>
              <CardContent>
                <Button className="w-full">Quản Lý Phòng Học</Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Giảng Viên</CardTitle>
              </CardHeader>
              <CardContent>
                <Button className="w-full">Quản Lý Giảng Viên</Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Cơ Sở</CardTitle>
              </CardHeader>
              <CardContent>
                <Button className="w-full">Quản Lý Cơ Sở</Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="import-export" className="space-y-4">
          <div className="grid gap-6 md:grid-cols-2">
            <Card className="cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => router.push('/admin/import-management')}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Database className="mr-2 h-5 w-5" />
                  Quản lý Import & Đồng bộ
                </CardTitle>
                <CardDescription>
                  Giao diện quản lý CRUD đầy đủ cho import và đồng bộ dữ liệu
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center text-sm text-gray-600">
                  <RefreshCw className="mr-2 h-4 w-4" />
                  Đồng bộ tự động từ hệ thống nguồn
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Upload className="mr-2 h-4 w-4" />
                  Upload file Excel/CSV
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <History className="mr-2 h-4 w-4" />
                  Lịch sử import chi tiết
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <Settings className="mr-2 h-4 w-4" />
                  Cấu hình đồng bộ
                </div>
                <Button className="w-full mt-4">
                  <ArrowRight className="mr-2 h-4 w-4" />
                  Mở Quản lý Import
                </Button>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Download className="mr-2 h-5 w-5" />
                  Export Template
                </CardTitle>
                <CardDescription>
                  Tải template Excel để import dữ liệu
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <Button className="w-full justify-start" variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Template Khoa
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Template Môn Học
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Template Lớp Học
                </Button>
                <Button className="w-full justify-start" variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Template Giảng Viên
                </Button>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  )
}
